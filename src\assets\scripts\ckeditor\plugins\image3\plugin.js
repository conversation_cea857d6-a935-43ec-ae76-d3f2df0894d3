'use strict';

( function() {

	CKEDITOR.plugins.add( 'image3', {
		requires: 'widget,dialog',
		icons: 'image',
		hidpi: true,

		onLoad: function() {
			CKEDITOR.addCss(
			'.cke_widget_image3{' +
				// This is to remove unwanted space so resize
				// wrapper is displayed property.
				'line-height:0' +
			'}' +
			'.cke_editable.cke_image_sw, .cke_editable.cke_image_sw *{cursor:sw-resize !important}' +
			'.cke_editable.cke_image_se, .cke_editable.cke_image_se *{cursor:se-resize !important}' +
			'.cke_image_resizer{' +
				'display:none;' +
				'position:absolute;' +
				'width:10px;' +
				'height:10px;' +
				'bottom:-5px;' +
				'right:-5px;' +
				'background:#000;' +
				'outline:1px solid #fff;' +
				// Prevent drag handler from being misplaced (https://dev.ckeditor.com/ticket/11207).
				'line-height:0;' +
				'cursor:se-resize;' +
			'}' +
			'.cke_image_resizer_wrapper{' +
				'position:relative;' +
				'display:inline-block;' +
				'line-height:0;' +
			'}' +
			// Bottom-left corner style of the resizer.
			'.cke_image_resizer.cke_image_resizer_left{' +
				'right:auto;' +
				'left:-5px;' +
				'cursor:sw-resize;' +
			'}' +
			'.cke_widget_wrapper:hover .cke_image_resizer,' +
			'.cke_image_resizer.cke_image_resizing{' +
				'display:block' +
			'}' +
			// Hide resizer in read only mode (#2816).
			'.cke_editable[contenteditable="false"] .cke_image_resizer{' +
				'display:none;' +
			'}' +
			// Expand widget wrapper when linked inline image.
			'.cke_widget_wrapper>a{' +
				'display:inline-block' +
			'}' );
		},

		init: function( editor ) {
			var widgetDef = getWidgetDefinition( editor );

			// Register the widget.
			editor.widgets.add( 'image3', widgetDef );

			// Add toolbar button.
			editor.ui.addButton && editor.ui.addButton( 'Image', {
				label: '图像属性',
				command: 'image3',
				toolbar: 'insert,10'
			} );

      // Register context menu option for editing widget.
			if ( editor.contextMenu ) {
				editor.addMenuGroup( 'image', 10 );

				editor.addMenuItem( 'image', {
					label: '图像属性',
					command: 'image3',
					group: 'image'
				} );
			}

			CKEDITOR.dialog.add( 'image3', this.path + 'dialogs/image3.js' );
		}
	} );

	// Returns the widget definition.
	function getWidgetDefinition( editor ) {
		return {
			// Widget definition.
			draggable: false, // Requirement: Disable drag-and-drop moving.
			allowedContent: 'img[alt,!src,width,height]{width,height,float,vertical-align}',
			requiredContent: 'img[src]',
			parts: {
				image: 'img'
			},
			dialog: 'image3',
			template: '<img src="" />',

			// Executed when an existing element is being converted into a widget.
			upcast: function( element ) {
				return element.name == 'img' && element.attributes.src;
			},

			// Executed when the widget is initialized.
			init: function() {

        function normalizeDimensionValue( val ) {
    if ( !val ) return '';
    // 去掉空白
    val = String( val ).trim();
    var m = val.match(/^(\d+)px$/i);
    if ( m ) return m[1]; // 返回纯数字，便于 dialog 联动
    // 如果是纯数字 '100' 也返回
    if ( val.match(/^\d+$/) ) return val;
    // 否则保留原始（如 '50%', 'auto' 等）
    return val;
}

				var img = this.parts.image;
				var data = {
					width: '',
					height: '',
					alignType: 'inline', // 'inline' or 'float'
					alignValue: 'baseline' // 'baseline', 'top', etc. OR 'left', 'right'
				};

				data.width = normalizeDimensionValue(img.getStyle( 'width' ) || img.getAttribute( 'width' ));
				data.height = normalizeDimensionValue(img.getStyle( 'height' ) || img.getAttribute( 'height' ));

				// Read alignment. Float takes precedence.
				var floatStyle = img.getStyle( 'float' );
				var verticalAlignStyle = img.getStyle( 'vertical-align' );

				if ( floatStyle === 'left' || floatStyle === 'right' ) {
					data.alignType = 'float';
					data.alignValue = floatStyle;
				} else if ( verticalAlignStyle ) {
					data.alignType = 'inline';
					data.alignValue = verticalAlignStyle;
				}

				this.setData( data );

				// Setup resizer.
				if ( editor.config.image3_disableResizer !== true ) {
					setupResizer( this );
				}

        this.on( 'contextMenu', function( evt ) {
					evt.data.image = CKEDITOR.TRISTATE_OFF;
				} );
			},

			// Called when widget.setData() is executed.
			data: function() {
				var img = this.parts.image;

				// Clear previous alignment and dimension styles/attributes.
				img.removeStyle( 'float' );
				img.removeStyle( 'vertical-align' );
				img.removeStyle( 'width' );
				img.removeStyle( 'height' );
				img.removeAttribute( 'width' );
				img.removeAttribute( 'height' );

				// Apply new dimensions to style.
				if ( this.data.width ) {
          // 如果是纯数字，则加 px；否则按原样（允许百分比、auto、100px 等）
          img.setStyle( 'width', ( this.data.width + '' ).match( /^\d+$/ ) ? this.data.width + 'px' : this.data.width );
        }
        if ( this.data.height ) {
          img.setStyle( 'height', ( this.data.height + '' ).match( /^\d+$/ ) ? this.data.height + 'px' : this.data.height );
        }

				// Apply new alignment.
				if ( this.data.alignType === 'float' ) {
					img.setStyle( 'float', this.data.alignValue );
				} else if ( this.data.alignType === 'inline' ) {
					img.setStyle( 'vertical-align', this.data.alignValue );
				}

        var wrapper = this.wrapper;
        wrapper.removeStyle( 'float' );
				// Apply new alignment style to the wrapper.
				if ( this.data.alignType === 'float' && this.data.alignValue ) {
					wrapper.setStyle( 'float', this.data.alignValue );
				}
			}
		};
	}

	// Defines all features related to drag-driven image resizing.
	function setupResizer( widget ) {
		var editor = widget.editor;
		var editable = editor.editable();
		var doc = editor.document;

		var resizer = widget.resizer = doc.createElement( 'span' );
		resizer.addClass( 'cke_image_resizer' );
		resizer.setAttribute( 'title', '缩放图片' ); // Resize image
		resizer.append( new CKEDITOR.dom.text( '\u200b', doc ) );

		// Append the resizer directly to the widget's main wrapper.
		// The wrapper is a span with display:inline-block, which is sufficient
		// to act as a positioning context for the absolutely positioned resizer.
		widget.wrapper.append( resizer );

		resizer.on( 'mousedown', function( evt ) {
			var image = widget.parts.image;
			var startX = evt.data.$.screenX;
			var startY = evt.data.$.screenY;
			var startWidth = image.$.clientWidth;
			var startHeight = image.$.clientHeight;
			var ratio = startWidth / startHeight;
			var listeners = [];

			var factor = ( widget.data.alignType === 'float' && widget.data.alignValue === 'right' ) ? -1 : 1;
      var cursorClass = 'cke_image_s' + ( factor === -1 ? 'w' : 'e' );

			editor.fire( 'saveSnapshot' );

			function onMouseMove( evt ) {
				var nativeEvt = evt.data.$;
				var moveDiffX = nativeEvt.screenX - startX;
				var newWidth = startWidth + factor * moveDiffX;
				var newHeight = Math.round( newWidth / ratio );

				// Prevent image from becoming too small.
				if ( newWidth > 10 && newHeight > 10 ) {
					image.setStyle( 'width', newWidth + 'px' );
					image.setStyle( 'height', newHeight + 'px' );
				}
			}

			function onMouseUp() {
				var l;
				while ( ( l = listeners.pop() ) ) {
					l.removeListener();
				}
				editable.removeClass( cursorClass );
				resizer.removeClass( 'cke_image_resizing' );

				// Commit the new size to the widget data model.
				widget.setData( {
					width: image.getStyle( 'width' ),
					height: image.getStyle( 'height' )
				} );

				editor.fire( 'saveSnapshot' );
			}

			function attachToDocuments( name, callback, collection ) {
				var globalDoc = CKEDITOR.document;
				var listeners = [];
				if ( !doc.equals( globalDoc ) ) {
					listeners.push( globalDoc.on( name, callback ) );
				}
				listeners.push( doc.on( name, callback ) );
				if ( collection ) {
					for ( var i = listeners.length; i--; ) {
						collection.push( listeners.pop() );
					}
				}
			}

			attachToDocuments( 'mousemove', onMouseMove, listeners );
			attachToDocuments( 'mouseup', onMouseUp, listeners );

			editable.addClass( cursorClass );
			resizer.addClass( 'cke_image_resizing' );
		} );

		widget.on( 'data', function() {
			var isRightAligned = ( this.data.alignType === 'float' && this.data.alignValue === 'right' );
			resizer[ isRightAligned ? 'addClass' : 'removeClass' ]( 'cke_image_resizer_left' );
		} );
	}

} )();