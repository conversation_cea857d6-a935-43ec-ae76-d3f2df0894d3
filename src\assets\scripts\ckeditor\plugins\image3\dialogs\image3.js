'use strict';

CKEDITOR.dialog.add( 'image3', function( editor ) {
	// 切换锁定比例
	function toggleLockRatio(dialog, locked) {
		var lockButton = dialog.getContentElement( 'info', 'lock' ).getElement().getChild( 0 );
		lockButton[ locked ? 'removeClass' : 'addClass' ]( 'cke_btn_unlocked' );
	}
	
	// 切换显示对齐方式
	function toggleAlignmentFields( dialog, alignType ) {
		var alignInlineContainer = dialog.getContentElement( 'info', 'alignInline' ).getElement().getParent();
		var alignFloatContainer = dialog.getContentElement( 'info', 'alignFloat' ).getElement().getParent();

		if ( alignType === 'inline' ) {
			alignInlineContainer.show();
			alignFloatContainer.hide();
		} else { // 'float'
			alignInlineContainer.hide();
			alignFloatContainer.show();
		}
	}

	// 更新关联的宽高
	function updateLinkedField() {
		var dialog = this.getDialog();
		if ( !dialog.isRatioLocked ) {
			return;
		}

		var value = this.getValue();
		if ( !value || !value.match( /^\d+$/ ) ) {
			return;
		}
		value = parseInt( value, 10 );

		var isWidth = ( this.id == 'width' )
		var linkedField = dialog.getContentElement( 'info', isWidth ? 'height' : 'width' );

		if ( dialog.originalWidth > 0 && dialog.originalHeight > 0 ) {
			var ratio = dialog.originalWidth / dialog.originalHeight;
			var newValue = isWidth ? Math.round( value / ratio ) : Math.round( value * ratio );
			linkedField.setValue( newValue );
		}
	}

	// --- Dialog Definition ---

	return {
		title: '图像属性',
		width: 250,

		onShow: function() {
			// Store image-specific data on the dialog instance for this session.
			var widget = this.getModel();
			var image = widget.parts.image;

			this.originalWidth = image.$.naturalWidth;
			this.originalHeight = image.$.naturalHeight;

			this.isRatioLocked = true;

			// Populate all fields from widget data.
			this.setupContent( widget.data );

			toggleLockRatio(this, this.isRatioLocked);
			console.log(widget.data);
			// Set initial visibility of alignment fields.
			toggleAlignmentFields( this, widget.data.alignType );
		},

		onOk: function() {
			var widget = this.getModel();
			var data = {};
			this.commitContent( data ); // Get data from all fields.

			// Consolidate alignment value.
			data.alignValue = ( data.alignType === 'inline' ) ? data.alignInline : data.alignFloat;
			delete data.alignInline;
			delete data.alignFloat;

			widget.setData( data );
		},

		contents: [ {
			id: 'info',
			label: '基本信息',
			elements: [
				// Dimension controls
				{
					type: 'hbox',
					widths: [ '80px', '80px', '90px' ],
					children: [
						{
							type: 'text',
							id: 'width',
							label: '宽度',
							width: '80px',
							onKeyUp: updateLinkedField,
							setup: function( data ) { this.setValue( data.width ); },
							commit: function( data ) { data.width = this.getValue(); }
						},
						{
							type: 'text',
							id: 'height',
							label: '高度',
							width: '80px',
							onKeyUp: updateLinkedField,
							setup: function( data ) { this.setValue( data.height ); },
							commit: function( data ) { data.height = this.getValue(); }
						},
						{
							type: 'html',
							id: 'lock',
							style: 'margin-top:22px;width:40px;height:20px;',
							onLoad: function() {
								var dialog = this.getDialog();
								var lockButton = this.getElement().getChild( 0 );
								var resetButton = this.getElement().getChild( 1 );

								// Lock button click handler.
								lockButton.on( 'click', function( evt ) {
									dialog.isRatioLocked = !dialog.isRatioLocked;
									toggleLockRatio(dialog, dialog.isRatioLocked);
									if ( dialog.isRatioLocked ) {
										updateLinkedField.call( dialog.getContentElement( 'info', 'width' ) );
									}
									evt.data.preventDefault();
								} );

								// Reset button click handler.
								resetButton.on( 'click', function( evt ) {
									dialog.getContentElement( 'info', 'width' ).setValue( dialog.originalWidth );
									dialog.getContentElement( 'info', 'height' ).setValue( dialog.originalHeight );
									evt.data.preventDefault();
								} );
							},
							html: '<div>' +
								'<a href="javascript:void(0)" tabindex="-1" title="锁定比例" class="cke_btn_locked" role="checkbox"><span class="cke_icon"></span><span class="cke_label">锁定比例</span></a>' +
								'<a href="javascript:void(0)" tabindex="-1" title="原始尺寸" class="cke_btn_reset" role="button"><span class="cke_label">原始尺寸</span></a>' +
								'</div>'
						}
					]
				},
				// Alignment controls
				{
					type: 'hbox',
					widths: [ '80px', '80px', '90px' ],
					children: [
						{
							type: 'select',
							id: 'alignType',
							label: '对齐方式',
							items: [ [ '嵌入型', 'inline' ], [ '环绕型', 'float' ] ],
							'default': 'inline',
							onChange: function() { toggleAlignmentFields( this.getDialog(), this.getValue() ); },
							setup: function( data ) { this.setValue( data.alignType || 'inline' ); },
							commit: function( data ) { data.alignType = this.getValue(); }
						},
						{
							type: 'select',
							id: 'alignInline',
							label: '垂直对齐',
							items: [ [ '基线', 'baseline' ], [ '顶部', 'top' ], [ '居中', 'middle' ], [ '底部', 'bottom' ] ],
							'default': 'baseline',
							setup: function( data ) { if ( data.alignType === 'inline' ) this.setValue( data.alignValue ); },
							commit: function( data ) { data.alignInline = this.getValue(); }
						},
						{
							type: 'select',
							id: 'alignFloat',
							label: '水平对齐',
							items: [ [ '居左', 'left' ], [ '居右', 'right' ] ],
							'default': 'left',
							setup: function( data ) { if ( data.alignType === 'float' ) this.setValue( data.alignValue ); },
							commit: function( data ) { data.alignFloat = this.getValue(); }
						},
						{
							type: 'html',
							id: 'empty',
							html: '<div></div>'
						}
					]
				}
			]
		} ]
	};
} );